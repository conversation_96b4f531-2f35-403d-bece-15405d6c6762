import {ReactQuery} from "@src/@types";

// khai báo interface props Context của Login
export interface BaoHiemTaiSanContextProps {
  danhSachHopDongBaoHiemTaiSan: Array<CommonExecute.Execute.IBaoHiemTaiSan>;
  danhSachKhachHang: Array<CommonExecute.Execute.IKhachHang>;
  loading: boolean;
  tongSoDong: number;
  defaultFormValue: object;
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  listSanPham: Array<CommonExecute.Execute.ISanPham>;
  listChiNhanh: Array<CommonExecute.Execute.IChiNhanh>;
  listPhongBan: Array<CommonExecute.Execute.IPhongBan>;
  listPhuongThucKhaiThac: Array<CommonExecute.Execute.IPhuongThucKhaiThac>;
  listChuongTrinhBaoHiem: Array<CommonExecute.Execute.IChuongTrinhBaoHiem>;
  listDonViBoiThuong: Array<CommonExecute.Execute.INhaBaoHiemTPA>;
  filterParams: ReactQuery.ITimKiemPhanTrangHopDongBaoHiemTaiSanParams & ReactQuery.IPhanTrang;
  tongSoDongCanBoQuanLy: number;
  tongSoDongDataLDaiLy: number;
  tongSoDongDataKhachHang: number;
  listCanBo: Array<CommonExecute.Execute.IDoiTacNhanVien>;
  // listDaiLyKhaiThac: Array<CommonExecute.Execute.IDanhMucDaiLy>;
  // listKhachHang: Array<CommonExecute.Execute.IKhachHang>;
  danhSachDaiLy: Array<CommonExecute.Execute.IDanhMucDaiLy>;
  chiTietHopDongBaoHiemTaiSan: CommonExecute.Execute.IChiTietHopDongTaiSan | null;
  danhSachDoiTuongBaoHiemTaiSan: Array<CommonExecute.Execute.IBaoHiemTaiSan>;
  tongSoDongDoiTuongBaoHiemTaiSan: number;
  tongPhiBaoHiemTaiSanFromAPI: number;
  listNhomDoiTuong: Array<CommonExecute.Execute.INhomDoiTuong>;
  chiTietDoiTuongBaoHiemTaiSan: CommonExecute.Execute.IDoiTuongBaoHiemTaiSan | null;
  listTinhThanh: Array<CommonExecute.Execute.IDanhMucTinhThanh>;
  listPhuongXa: Array<CommonExecute.Execute.IDanhMucPhuongXa>;
  listDongBaoHiemHopDongTaiSan: Array<CommonExecute.Execute.IDongBaoHiem>;
  listTaiBaoHiemHopDongTaiSan: Array<CommonExecute.Execute.ITaiBaoHiem>;
  listThongTinThanhToanHopDongTaiSan: Array<CommonExecute.Execute.IThongTinThanhToanCuaHopDong>;
  listDonViDongTai: Array<CommonExecute.Execute.IDonViDongTai>;
  setListPhuongXa: React.Dispatch<React.SetStateAction<Array<CommonExecute.Execute.IDanhMucPhuongXa>>>;
  getListPhuongXa: (params: ReactQuery.ILietKeDanhSachPhuongXaParams) => Promise<void>;
  layChiTietDoiTuongBaoHiemTaiSan: (params: ReactQuery.IChiTietDoiTuongBaoHiemTaiSanParams) => Promise<CommonExecute.Execute.IDoiTuongBaoHiemTaiSan | null>;
  updateDoiTuongBaoHiemTaiSan: (params: ReactQuery.ICapNhatDoiTuongBaoHiemTaiSanParams) => Promise<boolean>;
  timKiemPhanTrangDoiTuongBaoHiemTaiSan: (params: ReactQuery.ITimKiemPhanTrangDoiTuongBaoHiemXeParams) => void;
  timKiemPhanTrangKhachHang: (params: ReactQuery.ILayDanhSachKhachHangPhanTrangParams) => void;
  timKiemPhanTrangDaiLy: (params: ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams) => void;
  getListCanBoQuanLy: (params: ReactQuery.ITimKiemPhanTrangCanBoQuanLyParams & ReactQuery.IPhanTrang) => void;
  getListDaiLyKhaiThac: (params: ReactQuery.ILietKeDanhSachDaiLyParams) => Promise<{data: Array<CommonExecute.Execute.IDanhMucDaiLy>}>;
  layChiTietHopDongBaoHiemTaiSan: (params: ReactQuery.IChiTietHopDongXeParams) => Promise<CommonExecute.Execute.IChiTietHopDongTaiSan | null>;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangHopDongBaoHiemTaiSanParams & ReactQuery.IPhanTrang>>;
  timKiemPhanTrangHopDongBaoHiemTaiSan: (params: ReactQuery.ITimKiemPhanTrangHopDongBaoHiemTaiSanParams) => void;
  updateHopDongBaoHiemTaiSan: (params: ReactQuery.IUpdateHopDongParams) => Promise<{
    success: boolean;
    isNewContract: boolean;
    contractInfo?: {
      so_id: number;
      so_hd: string;
    };
  }>;
  resetChiTietHopDongBaoHiemTaiSan: () => void;
  huyHopDongTaiSan: () => Promise<boolean>;
  goHuyHopDongTaiSan: () => Promise<boolean>;
  taoHopDongSuaDoiBoSung: (params: ReactQuery.ITaoHopDongSuaDoiBoSungParams) => Promise<{so_id: number; so_hd: string} | null>;

  getDanhSachFileThumbnailTheoDoiTuong: (params: ReactQuery.IGetFileThumbnailParams) => Promise<any>;
  uploadFileTheoDoiTuong: (params: ReactQuery.IUploadFileTheoDoiTuongXeParams) => Promise<boolean>;
  deleteFileTheoDoiTuong: (params: ReactQuery.IUploadFileTheoDoiTuongXeParams) => Promise<boolean>;
  phanLoaiFileTheoHangMucXe: (params: ReactQuery.IPhanLoaiFileTheoHangMucXeParams) => Promise<boolean>;
  layDanhSachCauHinhDongCuaHopDongBaoHiemTaiSan: (params: ReactQuery.ILayThongTinDongTaiBHCuaHopDongBaoHiemParams) => Promise<any>;
  layDanhSachCauHinhTaiBHCuaHopDongBaoHiemTaiSan: (params: ReactQuery.ILayThongTinDongTaiBHCuaHopDongBaoHiemParams) => Promise<any>;
  layThongTinThanhToanCuaHopDongBaoHiemTaiSan: (params: ReactQuery.ILayThongTinThanhToanCuaHopDongBaoHiemParams) => Promise<any>;
  layChiTietThongTinCauHinhDongBH: (params: ReactQuery.IChiTietThongTinCauHinhDongBHParams) => Promise<CommonExecute.Execute.IDongBaoHiem | null>;
  xoaThongTinDongBH: (params: ReactQuery.IChiTietThongTinCauHinhDongBHParams) => Promise<boolean>;
  lietKeDanhSachCacDoiTuongDaDuocApDungDongBH: (params: ReactQuery.IChiTietThongTinCauHinhDongBHParams) => Promise<any>;
  updateDoiTuongApDungTyLeDongBH: (params: ReactQuery.IUpdateDoiTuongApDungDongBHParams) => Promise<boolean>;
  updateCauHinhDongBaoHiem: (params: ReactQuery.IUpdateCauHinhDongBHParams) => Promise<boolean>;
  layChiTietThongTinCauHinhTaiBH: (params: ReactQuery.IChiTietThongTinCauHinhTaiBHParams) => Promise<CommonExecute.Execute.ITaiBaoHiem | null>;

  xoaThongTinTaiBH: (params: ReactQuery.IChiTietThongTinCauHinhTaiBHParams) => Promise<boolean>;
  updateCauHinhTaiBaoHiem: (params: ReactQuery.IUpdateCauHinhTaiBHParams) => Promise<boolean>;
  lietKeDanhSachCacDoiTuongDaDuocApDungTaiBH: (params: ReactQuery.IChiTietThongTinCauHinhTaiBHParams) => Promise<any>;
  updateDoiTuongApDungTaiBH: (params: ReactQuery.IUpdateDoiTuongApDungTaiBHParams) => Promise<boolean>;
  layChiTietKyThanhToan: (params: ReactQuery.IChiTietKyThanhToanParams) => Promise<any>;
  updateKyThanhToan: (params: ReactQuery.IUpdateKyThanhToanParams) => Promise<boolean>;
  exportPdfHopDong: (params: ReactQuery.IExportPDFHopDongParams) => Promise<any>;
}
