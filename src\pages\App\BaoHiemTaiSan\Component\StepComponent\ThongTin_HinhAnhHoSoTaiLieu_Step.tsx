import {DeleteOutlined, ExportOutlined, LeftOutlined, PrinterOutlined, RightOutlined, UploadOutlined, TagsOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {FormInput, ModalQuanLyFileCaNhan} from "@src/components";
import Image from "@src/components/Image";
import {env, formatCurrencyUS} from "@src/utils";
import {Button, Checkbox, Col, Divider, Form, Row, Table, Tooltip, message, Popover} from "antd";
import {useCallback, useState, useEffect, useRef, useMemo} from "react";
import {useBaoHiemTaiSanContext} from "../../index.context";
import { tableDoiTuongColumn, TableDoiTuongColumnDataType} from "./Constant";
// import {NHOM_XCG} from "../../index.configs";
import {Worker, Viewer} from "@react-pdf-viewer/core";
import "@react-pdf-viewer/core/lib/styles/index.css";
import workerUrl from "pdfjs-dist/build/pdf.worker.js?url";
import {RenderTableDoiTuongBaoHiemTaiSan_Component} from "../RenderTableDoiTuongBaoHemTaiSan_Component";
// import PhanLoaiPopoverContent from "./PhanLoaiFile_PopoverContent";

// const {nd_tim} = FormTimKiemDoiTuongBaoHiemTaiSan;
interface FileCategory {
  id: string;
  name: string;
  files: {type: string; url: string; name: string; id?: number}[];
}

const PAGE_SIZE = 10;
const UNCLASSIFIED_ID = "UNCLASSIFIED";
const UNCLASSIFIED_NAME = "Ảnh chưa phân loại";

const getCategoryInfo = (file: any) => {
  if (!file.ma_hang_muc || file.ma_hang_muc === "HANG_MUC_KHAC") {
    return {id: UNCLASSIFIED_ID, name: UNCLASSIFIED_NAME};
  }
  return {id: file.ma_hang_muc, name: file.ten_hang_muc || file.ma_hang_muc};
};

const getFullUrl = (url: string, domain: string) => {
  if (!url) return "";
  if (url.startsWith("http://") || url.startsWith("https://")) return url;
  return domain.replace(/\/$/, "") + (url.startsWith("/") ? url : "/" + url);
};

// Sửa hàm xác định loại file
function getFileType(extension?: string, url?: string): string {
  let ext = (extension || "").replace(/^\./, "").toLowerCase();
  if (!ext && url) {
    ext = url.split(".").pop()?.toLowerCase() || "";
  }
  if (ext.includes("pdf")) return "pdf";
  if (["jpg", "jpeg", "png", "gif", "webp", "bmp"].some(imgExt => ext.includes(imgExt))) return "image";
  return "other";
}

const ThongTin_HinhAnhHoSoTaiLieu_HopDongXe_Step = () => {
  // --- BẮT ĐẦU: Logic lấy danh sách đối tượng xe giống step 2 ---
  const {
    danhSachDoiTuongBaoHiemTaiSan,
    loading,
    tongSoDongDoiTuongBaoHiemTaiSan,
    tongPhiBaoHiemTaiSanFromAPI,
    timKiemPhanTrangDoiTuongBaoHiemTaiSan,
    layChiTietDoiTuongBaoHiemTaiSan,
    chiTietHopDongBaoHiemTaiSan,
    layChiTietHopDongBaoHiemTaiSan,
    getDanhSachFileThumbnailTheoDoiTuong,
    uploadFileTheoDoiTuong,
    deleteFileTheoDoiTuong,
    phanLoaiFileTheoHangMucXe,
  } = useBaoHiemTaiSanContext();
  const [doiTuongTaiSanSelected, setDoiTuongTaiSanSelected] = useState<any>(null);
  const [formTimKiemDoiTuongXe] = Form.useForm();
  const [searchParams, setSearchParams] = useState({nd_tim: ""});
  const [page, setPage] = useState(1);

  // State lưu categories lấy từ API
  const [categories, setCategories] = useState<FileCategory[]>([]);

  // State lưu selectedCategory và selectedFile dựa trên categories thực tế
  const [selectedCategory, setSelectedCategory] = useState<string | undefined>(undefined);
  const [selectedFile, setSelectedFile] = useState<any>(undefined);
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);

  // State cho modal upload
  const [isUploadModalVisible, setIsUploadModalVisible] = useState(false);
  const [uploadingFiles, setUploadingFiles] = useState<File[]>([]);
  const [uploading, setUploading] = useState(false);
  const [phanLoaiPopoverVisible, setPhanLoaiPopoverVisible] = useState(false);

  // State cho modal phân loại file
  const [isPhanLoaiModalVisible, setIsPhanLoaiModalVisible] = useState(false);

  // Ref cho input file
  const fileInputRef = useRef<HTMLInputElement>(null);
  // Ref cho modal quản lý file cá nhân
  const modalQuanLyFileRef = useRef<any>(null);

  // Khi categories thay đổi, tự động chọn category và file đầu tiên nếu có
  useEffect(() => {
    if (categories.length > 0) {
      setSelectedCategory(categories[0].id);
      setSelectedFile(categories[0].files[0]);
    } else {
      setSelectedCategory(undefined);
      setSelectedFile(undefined);
    }
  }, [categories]);

  // Hàm nhận diện loại file dựa vào đuôi file
  function getFileTypeByUrl(url: string | undefined): string {
    if (!url) return "image";
    const ext = url.split(".").pop()?.toLowerCase();
    if (ext === "pdf") return "pdf";
    // Có thể bổ sung thêm các loại khác nếu cần
    return "image";
  }

  //conver mảng để hiển thị ảnh theo hạng mục
  const convertListFileThumbnailToCategories = (listFileThumbnail: Array<any>): FileCategory[] => {
    const domain = env.VITE_BASE_URL;
    const grouped: Record<string, FileCategory> = {};
    listFileThumbnail.forEach((file: any) => {
      const {id, name} = getCategoryInfo(file);
      if (!grouped[id]) {
        grouped[id] = {id, name, files: []};
      }
      const url = getFullUrl(file.url_file, domain);
      grouped[id].files.push({
        type: getFileType(file.extension, url), // Sử dụng includes thay vì ===
        url,
        name: file.ten_file || "",
        id: file.id_file as number | undefined, // chuẩn hóa sang id
      });
    });
    return Object.values(grouped);
  };

  //bấm chọn 1 đối tượng
  const handleSelectDoiTuongBaoHiemTaiSan = useCallback(
    async (record: TableDoiTuongColumnDataType) => {
      if (!record.so_id || (record.key && record.key.toString().includes("empty"))) return;
      const [chiTietDoiTuong, listFileThumbnail] = await Promise.all([
        layChiTietDoiTuongBaoHiemTaiSan(record as ReactQuery.IChiTietDoiTuongBaoHiemTaiSanParams),
        getDanhSachFileThumbnailTheoDoiTuong({
          so_id: record?.so_id ? Number(record.so_id) : undefined,
          so_id_dt: record?.so_id_dt ? Number(record.so_id_dt) : undefined,
          nv: "TS",
        }),
      ]);
      setDoiTuongTaiSanSelected(chiTietDoiTuong || null);
      const newCategories = convertListFileThumbnailToCategories(listFileThumbnail || []);
      setCategories(newCategories);
    },
    [layChiTietDoiTuongBaoHiemTaiSan, getDanhSachFileThumbnailTheoDoiTuong, chiTietHopDongBaoHiemTaiSan?.so_id, convertListFileThumbnailToCategories],
  );
 useMemo
  // Tìm hạng mục chứa file đang được chọn
  const getCategoryByFile = (file: any) => {
    return categories.find(cat => cat.files.some(f => f.url === file.url));
  };

  // Lấy danh sách file của hạng mục đang chọn
  const currentCategory = selectedFile ? getCategoryByFile(selectedFile) : categories.find(cat => cat.id === selectedCategory);
  const currentFiles = currentCategory ? currentCategory.files : [];

  // Tính toán vị trí hiện tại trong tổng số file
  const getAllFiles = () => {
    return categories.flatMap(cat => cat.files);
  };

  const getCurrentFileIndex = () => {
    if (!selectedFile) return -1;
    const allFiles = getAllFiles();
    return allFiles.findIndex(file => file.url === selectedFile.url);
  };

  const getTotalFiles = () => {
    return getAllFiles().length;
  };

  // Hàm điều hướng
  const navigateToNext = () => {
    if (!selectedFile || !currentFiles.length) return;
    const currentIndex = currentFiles.findIndex(file => file.url === selectedFile.url);
    const nextIndex = currentIndex + 1;

    if (nextIndex < currentFiles.length) {
      // Chuyển đến file tiếp theo trong cùng hạng mục
      setSelectedFile(currentFiles[nextIndex]);
    } else {
      // Đã đến cuối hạng mục hiện tại, chuyển sang hạng mục tiếp theo
      const currentCategoryIndex = categories.findIndex(cat => cat.id === selectedCategory);
      const nextCategoryIndex = (currentCategoryIndex + 1) % categories.length;
      const nextCategory = categories[nextCategoryIndex];

      if (nextCategory && nextCategory.files.length > 0) {
        setSelectedCategory(nextCategory.id);
        setSelectedFile(nextCategory.files[0]);
      }
    }
  };

  const navigateToPrevious = () => {
    if (!selectedFile || !currentFiles.length) return;
    const currentIndex = currentFiles.findIndex(file => file.url === selectedFile.url);
    const prevIndex = currentIndex - 1;

    if (prevIndex >= 0) {
      // Chuyển đến file trước đó trong cùng hạng mục
      setSelectedFile(currentFiles[prevIndex]);
    } else {
      // Đã đến đầu hạng mục hiện tại, chuyển sang hạng mục trước đó
      const currentCategoryIndex = categories.findIndex(cat => cat.id === selectedCategory);
      const prevCategoryIndex = currentCategoryIndex === 0 ? categories.length - 1 : currentCategoryIndex - 1;
      const prevCategory = categories[prevCategoryIndex];

      if (prevCategory && prevCategory.files.length > 0) {
        setSelectedCategory(prevCategory.id);
        setSelectedFile(prevCategory.files[prevCategory.files.length - 1]);
      }
    }
  };

  // Hàm render file dựa trên loại
  const renderFile = (file: any) => {
    if (file.type === "image") {
      console.log("file.url", file.url);

      return (
        <Image
          src={file.url}
          width="100%"
          height="100%"
          style={{
            objectFit: "contain",
            border: "1px solid #eee",
            borderRadius: 8,
            boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
            display: "block",
          }}
          alt={file.name}
          // fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
          preview={{visible: true}}
        />
      );
    } else if (file.type === "pdf") {
      return (
        <div style={{width: "100%", height: "92%", border: "1px solid #eee", borderRadius: 8, boxShadow: "0 2px 8px rgba(0,0,0,0.1)", display: "block"}}>
          <Worker workerUrl={workerUrl}>
            <Viewer fileUrl={file.url} />
          </Worker>
        </div>
      );
    }
    return null;
  };

  // Hàm xử lý upload file
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    const fileList = Array.from(files);
    const validFiles: File[] = [];
    const invalidFiles: string[] = [];

    fileList.forEach(file => {
      // Kiểm tra loại file
      const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"];
      if (!allowedTypes.includes(file.type)) {
        invalidFiles.push(`${file.name} - Không hỗ trợ định dạng này`);
        return;
      }

      // Kiểm tra kích thước file (giới hạn 10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        invalidFiles.push(`${file.name} - File quá lớn (>10MB)`);
        return;
      }

      validFiles.push(file);
    });

    if (invalidFiles.length > 0) {
      message.error(`Các file không hợp lệ:\n${invalidFiles.join("\n")}`);
    }

    if (validFiles.length > 0) {
      setUploadingFiles(prev => [...prev, ...validFiles]);
      setIsUploadModalVisible(true);
      message.success(`Đã thêm ${validFiles.length} file vào danh sách upload`);
    }

    // Reset input để có thể chọn lại cùng file
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // Hàm mở dialog chọn file
  const handleUploadClick = () => {
    // Mở modal quản lý file cá nhân thay vì modal upload files
    console.log("handleUploadClick called, modalQuanLyFileRef:", modalQuanLyFileRef.current);
    if (modalQuanLyFileRef.current?.open) {
      modalQuanLyFileRef.current.open();
      console.log("Modal opened successfully");
    } else {
      console.log("Modal ref not available");
    }
  };

  // Hàm xử lý gắn file vào hợp đồng/đối tượng xe
  const handleAttachFiles = async (fileSelected: any[]) => {
    if (!doiTuongTaiSanSelected || !chiTietHopDongBaoHiemTaiSan) {
      message.error("Chưa chọn hợp đồng hoặc đối tượng xe.");
      return;
    }
    try {
      // Sử dụng uploadFileTheoDoiTuong để gắn file vào đối tượng xe
      const responseUpload = await uploadFileTheoDoiTuong({
        so_id: chiTietHopDongBaoHiemTaiSan.so_id!,
        so_id_dt: doiTuongTaiSanSelected.gcn?.so_id_dt!,
        file: fileSelected.map(file => ({id: file.id})),
      } as ReactQuery.IUploadFileTheoDoiTuongXeParams);

      if (responseUpload) {
        modalQuanLyFileRef.current.close();
        // Refresh danh sách file sau khi gắn thành công
        if (doiTuongTaiSanSelected?.gcn?.so_id_dt) {
          const listFileThumbnail = await getDanhSachFileThumbnailTheoDoiTuong({
            so_id: chiTietHopDongBaoHiemTaiSan.so_id,
            so_id_dt: doiTuongTaiSanSelected.gcn.so_id_dt,
            nv: "TS",
          });
          const newCategories = convertListFileThumbnailToCategories(listFileThumbnail || []);
          setCategories(newCategories);
        }
      }
    } catch (err) {
      console.error("Lỗi khi gắn file:", err);
      message.error("Gắn file thất bại!");
    }
  };

  // Hàm xử lý xóa file
  const handleDeleteFiles = async () => {
    if (!doiTuongTaiSanSelected || !chiTietHopDongBaoHiemTaiSan) {
      message.error("Chưa chọn hợp đồng hoặc đối tượng xe.");
      return;
    }
    // Lấy danh sách file object từ categories theo selectedFiles (url)
    const filesToDelete = categories.flatMap(cat => cat.files).filter(f => selectedFiles.includes(f.url));
    if (filesToDelete.length === 0) {
      message.warning("Vui lòng chọn file để xóa.");
      return;
    }
    try {
      const responseDeleteFile = await deleteFileTheoDoiTuong({
        so_id: chiTietHopDongBaoHiemTaiSan.so_id!,
        so_id_dt: doiTuongTaiSanSelected.gcn?.so_id_dt!,
        file: filesToDelete.map(f => ({id: f.id})),
      } as ReactQuery.IUploadFileTheoDoiTuongXeParams);

      if (responseDeleteFile) {
        // Sau khi xóa thành công, làm mới danh sách file
        if (doiTuongTaiSanSelected?.gcn?.so_id_dt) {
          const listFileThumbnail = await getDanhSachFileThumbnailTheoDoiTuong({
            so_id: chiTietHopDongBaoHiemTaiSan.so_id,
            so_id_dt: doiTuongTaiSanSelected.gcn.so_id_dt,
            nv: "TS",
          });
          const newCategories = convertListFileThumbnailToCategories(listFileThumbnail || []);
          setCategories(newCategories);
          setSelectedFiles([]);
          setSelectedFile(undefined);
        }
      }
    } catch (err) {
      console.error("Lỗi khi xóa file:", err);
      message.error("Xóa file thất bại!");
    }
  };

  // Hàm xử lý phân loại file
  const handlePhanLoaiFiles = async (ma_hang_muc: string) => {
    if (!doiTuongTaiSanSelected || !chiTietHopDongBaoHiemTaiSan) {
      message.error("Chưa chọn hợp đồng hoặc đối tượng xe.");
      return;
    }
    const filesToUpdate = categories.flatMap(cat => cat.files).filter(f => selectedFiles.includes(f.url));
    if (filesToUpdate.length === 0) {
      message.warning("Vui lòng chọn file để phân loại.");
      return;
    }
    try {
      const response = await phanLoaiFileTheoHangMucXe({
        so_id: chiTietHopDongBaoHiemTaiSan.so_id!,
        so_id_dt: doiTuongTaiSanSelected.gcn?.so_id_dt!,
        ma_hang_muc: ma_hang_muc,
        file: filesToUpdate.map(f => ({id: f.id})),
      });
      if (response) {
        setIsPhanLoaiModalVisible(false);
        // Refresh lại danh sách file
        if (doiTuongTaiSanSelected?.gcn?.so_id_dt) {
          const listFileThumbnail = await getDanhSachFileThumbnailTheoDoiTuong({
            so_id: chiTietHopDongBaoHiemTaiSan.so_id,
            so_id_dt: doiTuongTaiSanSelected.gcn.so_id_dt,
            nv: "TS",
          });
          const newCategories = convertListFileThumbnailToCategories(listFileThumbnail || []);
          setCategories(newCategories);
          setSelectedFiles([]);
          setSelectedFile(undefined);
        }
      }
    } catch (err) {
      message.error("Phân loại file thất bại!");
    }
  };

  // Hàm render thumbnail dựa trên loại file
  const renderThumbnail = (file: any) => {
    const checked = selectedFiles.includes(file.url);
    const handleCheckboxChange = (e: any) => {
      if (e.target.checked) {
        setSelectedFiles(prev => [...prev, file.url]);
      } else {
        setSelectedFiles(prev => prev.filter(url => url !== file.url));
      }
    };
    const checkbox = (
      <Checkbox
        checked={checked}
        onChange={handleCheckboxChange}
        style={{position: "absolute", top: 2, left: 2, zIndex: 2, background: "rgba(255,255,255,0.7)", borderRadius: 2, padding: 0, margin: 0, width: 16, height: 16}}
      />
    );
    if (file.type === "image") {
      return (
        <div style={{position: "relative", width: 80, height: 80}}>
          {checkbox}
          <Image
            src={file.url + "&thumbnail=1"}
            width={80}
            height={80}
            style={{
              border: file.url === selectedFile?.url ? "2px solid #1890ff" : "1px solid #eee",
              borderRadius: 4,
              cursor: "pointer",
            }}
            onClick={() => {
              setSelectedFile(file);
              // Cập nhật selectedCategory khi chọn file từ hạng mục khác
              const fileCategory = getCategoryByFile(file);
              if (fileCategory && fileCategory.id !== selectedCategory) {
                setSelectedCategory(fileCategory.id);
              }
            }}
            // fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
            preview={false}
          />
        </div>
      );
    } else if (file.type === "pdf") {
      return (
        <div style={{position: "relative", width: 80, height: 80}}>
          {checkbox}
          <div
            style={{
              border: file.url === selectedFile?.url ? "2px solid #1890ff" : "1px solid #eee",
            }}
            className={`flex h-[80px] w-[80px] cursor-pointer items-center justify-center rounded border bg-[#f5f5f5] text-[24px] text-[#d32f2f] ${file.url === selectedFile?.url ? "border-2 border-[#1890ff]" : "border border-[#eee]"}`}
            onClick={() => {
              setSelectedFile(file);
              // Cập nhật selectedCategory khi chọn file từ hạng mục khác
              const fileCategory = getCategoryByFile(file);
              if (fileCategory && fileCategory.id !== selectedCategory) {
                setSelectedCategory(fileCategory.id);
              }
            }}>
            📄
          </div>
        </div>
      );
    }
    return null;
  };

  const renderFormInputColum = (props?: any, span = 4) => {
    return (
      <Col span={span}>
        <FormInput {...props} />
      </Col>
    );
  };

  const renderTableDanhSachDoiTuongXe = () => {
    return (
      <RenderTableDoiTuongBaoHiemTaiSan_Component
        columns={tableDoiTuongColumn || []}
        onRowClick={handleSelectDoiTuongBaoHiemTaiSan}
        className="custom-table-title-padding"
        pageSize={PAGE_SIZE}
        summaryRender={tongPhiBaoHiemTaiSanFromAPI => (
          <Table.Summary fixed>
            <Table.Summary.Row>
              <Table.Summary.Cell index={0} colSpan={1} className="!p-[8px]">
                <div className="text-center font-medium">Tổng phí bảo hiểm</div>
              </Table.Summary.Cell>
              <Table.Summary.Cell index={1} align="right" className="!p-[8px]">
                <div className="text-right font-medium">{formatCurrencyUS(tongPhiBaoHiemTaiSanFromAPI)}</div>
              </Table.Summary.Cell>
            </Table.Summary.Row>
          </Table.Summary>
        )}></RenderTableDoiTuongBaoHiemTaiSan_Component>
    );
  };
  return (
    <Row gutter={16} className="loverfow-hidden mt-4 h-full min-h-[500px]">
      {/* Cột trái: Danh sách đối tượng xe (thay thế danh sách tài liệu) */}
      <Col span={6} className="flex h-[81%] flex-col" style={{borderRight: "1px solid #eee"}}>
        <div className="mb-1 px-2 font-semibold">Danh sách đối tượng tài sản</div>
        {renderTableDanhSachDoiTuongXe()}
      </Col>

      {/* Cột giữa: Xem hình ảnh/PDF */}
      <Col span={14} className="flex h-full flex-col overflow-hidden px-4">
        <div className="relative flex min-h-[65vh] items-center justify-center overflow-hidden">
          {selectedFile ? (
            <div className="relative flex h-full w-full justify-center">
              {selectedFile.type === "image" ? (
                <Image
                  src={selectedFile.url}
                  width="100%"
                  style={{border: "1px solid #eee"}}
                  className="bg-white max-h-[65vh] max-w-full cursor-pointer rounded-lg border border-[#eee] object-contain shadow-[0_2px_8px_rgba(0,0,0,0.1)]"
                  alt={selectedFile.name}
                  // fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
                  preview={true}
                />
              ) : (
                renderFile(selectedFile)
              )}
            </div>
          ) : (
            <div
              style={{border: "1px solid #eee"}}
              className="flex h-[40vh] w-[40vw] flex-col justify-center rounded-lg bg-[#fafafa] text-center text-[16px] text-[#999] shadow-[0_2px_8px_rgba(0,0,0,0.1)]">
              <div style={{fontSize: 48, opacity: 0.5}}>📷</div>
              <div style={{marginBottom: 8}}>Chưa có file nào được chọn</div>
              <div style={{fontSize: 14, opacity: 0.7}}>Vui lòng chọn một file từ danh sách bên phải</div>
            </div>
          )}
        </div>
        {/* Navigation arrows below the image */}
        {selectedFile && getTotalFiles() > 1 && (
          <div className="mb-5 flex items-center justify-center gap-4">
            <Button type="primary" shape="circle" icon={<LeftOutlined />} onClick={navigateToPrevious} style={{background: "gray", border: "none", opacity: 0.4}} />
            <span style={{fontWeight: 500}}>
              {getCurrentFileIndex() + 1} / {getTotalFiles()}
            </span>
            <Button type="primary" shape="circle" icon={<RightOutlined />} onClick={navigateToNext} style={{background: "gray", border: "none", opacity: 0.4}} />
          </div>
        )}
      </Col>

      {/* Cột phải: Danh sách hạng mục và thumbnail */}
      <Col style={{borderLeft: "1px solid #eee"}} span={4} className="flex h-[81%] flex-col overflow-hidden">
        <div className="mb-1 px-2 font-semibold">Hạng mục & Files</div>
        <div className="flex-1 overflow-y-auto px-2">
          {categories.map((cat: FileCategory) => {
            const allFileUrls = cat.files.map((f: {url: string}) => f.url);
            const checkedCount = allFileUrls.filter((url: string) => selectedFiles.includes(url)).length;
            const allChecked = checkedCount === allFileUrls.length && allFileUrls.length > 0;
            const indeterminate = checkedCount > 0 && checkedCount < allFileUrls.length;
            const handleCategoryCheckbox = (e: any) => {
              if (e.target.checked) {
                // Chọn tất cả file trong hạng mục (không thêm trùng)
                setSelectedFiles((prev: string[]) => Array.from(new Set([...prev, ...allFileUrls])));
              } else {
                // Bỏ chọn tất cả file trong hạng mục
                setSelectedFiles((prev: string[]) => prev.filter((url: string) => !allFileUrls.includes(url)));
              }
            };
            return (
              <div key={cat.id} style={{marginBottom: 16}}>
                <div style={{display: "flex", alignItems: "center", marginBottom: 4, gap: 4}}>
                  <Checkbox checked={allChecked} indeterminate={indeterminate} onChange={handleCategoryCheckbox} style={{marginRight: 4}} />
                  <div
                    style={{
                      fontWeight: 500,
                      color: cat.id === selectedCategory ? "#1890ff" : undefined,
                      cursor: "pointer",
                    }}
                    onClick={() => {
                      // Khi bấm vào tên hạng mục, thực hiện check all hoặc uncheck all
                      if (allChecked) {
                        // Uncheck all
                        setSelectedFiles(prev => prev.filter((url: string) => !allFileUrls.includes(url)));
                      } else {
                        // Check all
                        setSelectedFiles(prev => Array.from(new Set([...prev, ...allFileUrls])));
                      }
                      setSelectedCategory(cat.id);
                      setSelectedFile(cat.files[0]);
                    }}>
                    {cat.name}
                  </div>
                </div>
                <div style={{display: "flex", gap: 8, flexWrap: "wrap"}}>
                  {cat.files.map((file: {url: string; name: string}) => (
                    <div key={file.url} style={{position: "relative"}}>
                      {renderThumbnail(file)}
                      <div className="text-gray-500 mt-0.5 max-w-[80px] overflow-hidden text-ellipsis whitespace-nowrap text-center text-[10px]">{file.name}</div>
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
        <Divider style={{margin: "12px 0"}} />
        <div className="flex justify-end gap-2 px-2">
          {/* Input file ẩn */}
          <input ref={fileInputRef} type="file" accept="image/*" multiple style={{display: "none"}} onChange={handleFileUpload} />
          <Tooltip title="Tải lên">
            <Button type="primary" icon={<UploadOutlined />} onClick={handleUploadClick} />
          </Tooltip>
          <Popover
            title="Phân loại hạng mục"
            // content={<PhanLoaiPopoverContent onSelect={handlePhanLoaiFiles} onClose={() => setPhanLoaiPopoverVisible(false)} />}
            trigger="click"
            open={phanLoaiPopoverVisible}
            onOpenChange={setPhanLoaiPopoverVisible}
            placement="left">
            <Tooltip title="Phân loại hạng mục">
              <Button type="primary" icon={<TagsOutlined />} disabled={selectedFiles.length === 0} onClick={() => setPhanLoaiPopoverVisible(true)} />
            </Tooltip>
          </Popover>
          {/* <Tooltip title="Import">
            <Button icon={<ImportOutlined />} />
          </Tooltip> */}
          <Tooltip title="Export">
            <Button icon={<ExportOutlined />} />
          </Tooltip>
          <Tooltip title="In">
            <Button icon={<PrinterOutlined />} />
          </Tooltip>

          <Tooltip title="Xóa">
            <Button danger icon={<DeleteOutlined />} disabled={selectedFiles.length === 0} onClick={handleDeleteFiles} />
          </Tooltip>
        </div>
      </Col>

      <ModalQuanLyFileCaNhan ref={modalQuanLyFileRef} onClickChonFile={handleAttachFiles} />
    </Row>
  );
};

export default ThongTin_HinhAnhHoSoTaiLieu_HopDongXe_Step;
